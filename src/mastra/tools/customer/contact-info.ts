import { createTool } from "@mastra/core/tools";
import { z } from "zod";

/**
 * Công cụ thu thập thông tin liên hệ từ khách hàng
 * Sử dụng để lưu lead v<PERSON><PERSON> hệ thống cho nhân viên tư vấn liên hệ sau
 */
export const getContactInfo = createTool({
  id: "get_contact_info",
  description: "Thu thập số điện thoại khách hàng để nhân viên tư vấn liên hệ hỗ trợ. Chỉ sử dụng khi khách hàng muốn được tư vấn trực tiếp hoặc cần hỗ trợ chuyên sâu.",
  inputSchema: z.object({
    phone: z.string().describe("Số điện thoại liên hệ của khách hàng (bắt buộc)"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Thu thập thông tin liên hệ
      console.log('📞 Đã thu thập số điện thoại:', context.phone);

      return {
        success: true,
        data: {
          phone: context.phone,
          tenant_id: tenant_id.toString(),
          collected_at: new Date().toISOString()
        },
        message: "Cảm ơn bạn đã cung cấp thông tin liên hệ! Nhân viên tư vấn sẽ liên hệ với bạn trong thời gian sớm nhất.",
      };
    } catch (error: any) {
      console.error("Lỗi khi thu thập thông tin liên hệ:", error);
      return {
        success: false,
        error: `Lỗi khi thu thập thông tin liên hệ: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
