import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getProductBySku } from "../../../services/postgres/product.service";

/**
 * @deprecated TOOL NÀY ĐÃ ĐƯỢC THAY THẾ BỞI getProductDetailsProductionTool
 *
 * TOOL TÌM KIẾM SẢN PHẨM THEO SKU - PRODUCTION
 *
 * ⚠️ DEPRECATED: Sử dụng getProductDetailsProductionTool thay thế
 * Tool này chuyên dụng để tìm kiếm sản phẩm theo mã SKU:
 * - Tự động tìm kiếm trong cả bảng products và product_variants
 * - Hiển thị thông tin giá chi tiết (giá gốc, giá sale, % giảm giá)
 * - Kiểm tra tồn kho
 * - Xử lý cả sản phẩm đơn giản và sản phẩm có biến thể
 * - T<PERSON><PERSON> về thông tin đầy đủ để khách hàng có thể đặt hàng
 */
export const getProductBySkuProductionTool = createTool({
  id: "get_product_by_sku_production",
  description: "Tìm kiếm sản phẩm theo mã SKU - Tool chuyên dụng: tự động tìm trong products và variants, hiển thị giá chi tiết, kiểm tra tồn kho",
  inputSchema: z.object({
    sku: z.string().describe("Mã SKU của sản phẩm hoặc biến thể"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log(`🔍 [SKU SEARCH] Đang tìm kiếm sản phẩm với SKU: ${context.sku}`);

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Sử dụng service getProductBySku để tìm kiếm
      const searchResult = await getProductBySku({
        sku: context.sku,
        tenant_id: tenant_id.toString(),
      });

      if (!searchResult.success) {
        return {
          success: false,
          error: `❌ Lỗi khi tìm kiếm sản phẩm: ${searchResult.message}`,
        };
      }

      if (!searchResult.data || searchResult.data.length === 0) {
        return {
          success: false,
          error: `❌ Không tìm thấy sản phẩm nào với SKU: ${context.sku}`,
        };
      }

      const productData = searchResult.data[0];

      // Kiểm tra xem có phải là kết quả từ variant không
      const isVariantResult = productData.variants && productData.variants.length > 0 &&
                             productData.variants[0].sku === context.sku;

      if (isVariantResult) {
        // TRƯỜNG HỢP 1: Tìm thấy biến thể cụ thể
        const variant = productData.variants[0];
        const originalPrice = parseFloat(variant.price);
        const finalPrice = variant.sale_price ? parseFloat(variant.sale_price) : originalPrice;
        const isOnSale = variant.sale_price && parseFloat(variant.sale_price) < originalPrice;
        const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

        return {
          success: true,
          product_info: {
            product_id: productData.id,
            variant_id: variant.id,
            product_name: productData.name,
            variant_name: variant.name,
            description: productData.description,
            short_description: productData.short_description,
            sku: variant.sku,
            type: productData.type,
            avatar: productData.avatar,
            images: productData.images,
            category_id: productData.category_id,

            // Thông tin giá chi tiết
            pricing: {
              original_price: originalPrice,
              final_price: finalPrice,
              is_on_sale: isOnSale,
              discount_percentage: discountPercentage,
              price_display: isOnSale ?
                `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}% từ ${originalPrice.toLocaleString('vi-VN')} VNĐ)` :
                `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
              savings: isOnSale ? originalPrice - finalPrice : 0,
              savings_display: isOnSale ? `Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ` : null,
            },

            // Thông tin tồn kho
            stock_info: {
              stock_quantity: variant.stock_quantity,
              in_stock: variant.stock_quantity > 0,
              stock_status: variant.stock_quantity > 10 ? 'Còn hàng' :
                           variant.stock_quantity > 0 ? `Chỉ còn ${variant.stock_quantity} sản phẩm` : 'Hết hàng',
            },

            attributes: variant.attributes,
            has_variants: true,
          },
          message: `✅ Tìm thấy biến thể "${variant.name}" của sản phẩm "${productData.name}" - SKU: ${context.sku} - Giá: ${finalPrice.toLocaleString('vi-VN')} VNĐ${isOnSale ? ` (Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ)` : ''}`,
        };
      } else {
        // TRƯỜNG HỢP 2: Tìm thấy sản phẩm chính (có thể có hoặc không có biến thể)
        if (productData.type === 'variable' && productData.variants && productData.variants.length > 0) {
          // Sản phẩm có biến thể - hiển thị tất cả biến thể
          const variants = productData.variants.map((v: { price: string; sale_price: string; id: any; name: any; sku: any; stock_quantity: number; attributes: any; }) => {
            const originalPrice = parseFloat(v.price);
            const finalPrice = v.sale_price ? parseFloat(v.sale_price) : originalPrice;
            const isOnSale = v.sale_price && parseFloat(v.sale_price) < originalPrice;
            const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

            return {
              id: v.id,
              name: v.name,
              sku: v.sku,
              pricing: {
                original_price: originalPrice,
                final_price: finalPrice,
                is_on_sale: isOnSale,
                discount_percentage: discountPercentage,
                price_display: isOnSale ?
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}%)` :
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
                savings: isOnSale ? originalPrice - finalPrice : 0,
              },
              stock_info: {
                stock_quantity: v.stock_quantity,
                in_stock: v.stock_quantity > 0,
                stock_status: v.stock_quantity > 10 ? 'Còn hàng' :
                             v.stock_quantity > 0 ? `Còn ${v.stock_quantity}` : 'Hết hàng',
              },
              attributes: v.attributes,
            };
          });

          // Tính giá range
          const prices = variants.map((v: { pricing: { final_price: any; }; }) => v.pricing.final_price);
          const minPrice = Math.min(...prices);
          const maxPrice = Math.max(...prices);

          return {
            success: true,
            product_info: {
              product_id: productData.id,
              product_name: productData.name,
              description: productData.description,
              short_description: productData.short_description,
              sku: productData.sku,
              type: productData.type,
              avatar: productData.avatar,
              images: productData.images,
              category_id: productData.category_id,

              // Thông tin giá range
              price_range: {
                min_price: minPrice,
                max_price: maxPrice,
                price_display: minPrice === maxPrice ?
                  `${minPrice.toLocaleString('vi-VN')} VNĐ` :
                  `${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VNĐ`,
              },

              // Thông tin tồn kho tổng
              stock_info: {
                total_stock: variants.reduce((sum: any, v: { stock_info: { stock_quantity: any; }; }) => sum + v.stock_info.stock_quantity, 0),
                available_variants: variants.filter((v: { stock_info: { in_stock: any; }; }) => v.stock_info.in_stock).length,
                total_variants: variants.length,
              },

              variants: variants,
              has_variants: true,
            },
            message: `✅ Tìm thấy sản phẩm "${productData.name}" (SKU: ${context.sku}) có ${variants.length} biến thể. Giá từ ${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VNĐ. Vui lòng chọn biến thể cụ thể để đặt hàng.`,
          };
        } else {
          // Sản phẩm đơn giản - không có biến thể
          const originalPrice = parseFloat(productData.price);
          const finalPrice = productData.sale_price ? parseFloat(productData.sale_price) : originalPrice;
          const isOnSale = productData.sale_price && parseFloat(productData.sale_price) < originalPrice;
          const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

          return {
            success: true,
            product_info: {
              product_id: productData.id,
              product_name: productData.name,
              description: productData.description,
              short_description: productData.short_description,
              sku: productData.sku,
              type: productData.type,
              avatar: productData.avatar,
              images: productData.images,
              category_id: productData.category_id,

              // Thông tin giá chi tiết
              pricing: {
                original_price: originalPrice,
                final_price: finalPrice,
                is_on_sale: isOnSale,
                discount_percentage: discountPercentage,
                price_display: isOnSale ?
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}% từ ${originalPrice.toLocaleString('vi-VN')} VNĐ)` :
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
                savings: isOnSale ? originalPrice - finalPrice : 0,
                savings_display: isOnSale ? `Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ` : null,
              },

              // Thông tin tồn kho
              stock_info: {
                stock_quantity: productData.stock_quantity,
                in_stock: productData.stock_quantity > 0,
                stock_status: productData.stock_quantity > 10 ? 'Còn hàng' :
                             productData.stock_quantity > 0 ? `Chỉ còn ${productData.stock_quantity} sản phẩm` : 'Hết hàng',
              },

              has_variants: false,
            },
            message: `✅ Tìm thấy sản phẩm "${productData.name}" (SKU: ${context.sku}) - Giá: ${finalPrice.toLocaleString('vi-VN')} VNĐ${isOnSale ? ` (Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ)` : ''}`,
          };
        }
      }
    } catch (error: any) {
      console.error("❌ Lỗi khi tìm kiếm sản phẩm theo SKU:", error);
      return {
        success: false,
        error: `Lỗi khi tìm kiếm sản phẩm theo SKU: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
