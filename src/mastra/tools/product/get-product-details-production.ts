import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getProductBySku } from "../../../services/postgres/product.service";
import { supabaseAdmin } from "../../../config/supabase";

/**
 * TOOL LẤY THÔNG TIN SẢN PHẨM CHI TIẾT - PRODUCTION
 *
 * Tool duy nhất tối ưu hóa để thay thế getProductPricingTool và getProductBySkuTool:
 * - Nhận vào product_id hoặc sku (hoặc cả hai)
 * - Tự động xử lý logic tìm kiếm trong products và product_variants
 * - Hiển thị thông tin giá chi tiết (giá gốc, giá sale, % giảm giá)
 * - Kiểm tra tồn kho và trạng thái sản phẩm
 * - Xử lý cả sản phẩm đơn giản và sản phẩm có biến thể
 * - Tối ưu hóa để tiết kiệm token và cung cấp thông tin chính xác
 */
export const getProductDetailsTool = createTool({
  id: "get_product_details",
  description: "Lấy thông tin chi tiết sản phẩm - Tool duy nhất: tìm kiếm theo product_id hoặc SKU, tự động xử lý logic giá, kiểm tra tồn kho, hiển thị biến thể",
  inputSchema: z.object({
    product_id: z
      .string()
      .nullable()
      .optional()
      .describe("Mã sản phẩm (tùy chọn - nếu không có sẽ tìm theo SKU)"),
    sku: z
      .string()
      .nullable()
      .optional()
      .describe("Mã SKU của sản phẩm hoặc biến thể (tùy chọn - nếu không có sẽ tìm theo product_id)"),
    variant_id: z
      .string()
      .nullable()
      .optional()
      .describe("Mã biến thể cụ thể (tùy chọn - chỉ dùng khi có product_id và muốn lấy biến thể cụ thể)"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
        product_id: context.product_id,
        sku: context.sku,
        variant_id: context.variant_id,
      });

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Kiểm tra input - phải có ít nhất product_id hoặc sku
      if (!context.product_id && !context.sku) {
        return {
          success: false,
          error: "Vui lòng cung cấp product_id hoặc sku để tìm kiếm sản phẩm",
        };
      }

      let productData: any = null;
      let searchMethod = "";

      // TRƯỜNG HỢP 1: Có product_id và variant_id - lấy biến thể cụ thể
      if (context.product_id && context.variant_id) {
        console.log(`📦 Lấy biến thể cụ thể: ${context.variant_id} của sản phẩm: ${context.product_id}`);
        searchMethod = "variant_specific";

        const { data: variantResult, error: variantError } = await supabaseAdmin
          .from('product_variants')
          .select(`
            id, name, sku, price, compare_at_price, stock_quantity, is_active, attributes,
            products!inner(id, name, description, short_description, type, avatar, images, is_active, category_id)
          `)
          .eq('id', context.variant_id)
          .eq('product_id', context.product_id)
          .eq('tenant_id', tenant_id)
          .eq('is_active', true)
          .eq('products.is_active', true)
          .single();

        if (variantError || !variantResult) {
          return {
            success: false,
            error: `❌ Không tìm thấy biến thể sản phẩm với ID: ${context.variant_id}`,
          };
        }

        const variant = variantResult;
        const product = (variant.products as any);
        productData = {
          id: product.id,
          name: product.name,
          description: product.description,
          short_description: product.short_description,
          type: product.type,
          avatar: product.avatar,
          images: product.images,
          category_id: product.category_id,
          variants: [{
            id: variant.id,
            name: variant.name,
            variant_name: variant.name,
            sku: variant.sku,
            price: variant.price,
            compare_at_price: variant.compare_at_price,
            stock_quantity: variant.stock_quantity,
            attributes: variant.attributes,
            is_active: variant.is_active,
          }],
        };
      }
      // TRƯỜNG HỢP 2: Có SKU - tìm kiếm theo SKU
      else if (context.sku) {
        searchMethod = "sku_search";

        const searchResult = await getProductBySku({
          sku: context.sku,
          tenant_id: tenant_id.toString(),
        });

        if (!searchResult.success || !searchResult.data || searchResult.data.length === 0) {
          return {
            success: false,
            error: `❌ Không tìm thấy sản phẩm nào với SKU: ${context.sku}`,
          };
        }

        productData = searchResult.data[0];
      }
      // TRƯỜNG HỢP 3: Có product_id - lấy thông tin sản phẩm
      else if (context.product_id) {
        console.log(`📦 Lấy thông tin sản phẩm: ${context.product_id}`);
        searchMethod = "product_id";

        const { data: productResult, error: productError } = await supabaseAdmin
          .from('products')
          .select('id, name, description, short_description, sku, price, compare_at_price, stock_quantity, type, avatar, images, is_active, category_id')
          .eq('id', context.product_id)
          .eq('tenant_id', tenant_id)
          .eq('is_active', true)
          .single();

        if (productError || !productResult) {
          return {
            success: false,
            error: `❌ Không tìm thấy sản phẩm với ID: ${context.product_id}`,
          };
        }

        const product = productResult as any;

        // Nếu sản phẩm có biến thể, lấy tất cả biến thể
        if (product.type === 'variable') {
          const { data: allVariants, error: variantsError } = await supabaseAdmin
            .from('product_variants')
            .select('id, name, sku, price, compare_at_price, stock_quantity, attributes, is_active')
            .eq('product_id', context.product_id)
            .eq('tenant_id', tenant_id)
            .eq('is_active', true)
            .order('name');

          if (variantsError) {
            console.error("❌ Lỗi khi lấy biến thể sản phẩm:", variantsError);
            return {
              success: false,
              error: `Lỗi khi lấy biến thể sản phẩm: ${variantsError.message}`,
            };
          }

          product.variants = allVariants || [];
        }

        productData = product;
      }

      if (!productData) {
        return {
          success: false,
          error: "❌ Không tìm thấy sản phẩm phù hợp",
        };
      }

      // XỬ LÝ VÀ ĐỊNH DẠNG DỮ LIỆU TRẢ VỀ
      return formatProductResponse(productData, searchMethod, context);

    } catch (error: any) {
      console.error("❌ Lỗi khi lấy thông tin sản phẩm:", error);
      return {
        success: false,
        error: `Lỗi khi lấy thông tin sản phẩm: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});

/**
 * Hàm helper để định dạng response sản phẩm
 */
function formatProductResponse(productData: any, searchMethod: string, context: any) {
  // Kiểm tra xem có phải là kết quả từ variant cụ thể không
  const isVariantSpecific = searchMethod === "variant_specific" || 
    (productData.variants && productData.variants.length === 1 && 
     productData.variants[0].sku === context.sku);

  if (isVariantSpecific) {
    // TRƯỜNG HỢP: Biến thể cụ thể
    const variant = productData.variants[0];
    const finalPrice = parseFloat(variant.price);
    const originalPrice = variant.compare_at_price ? parseFloat(variant.compare_at_price) : finalPrice;
    const isOnSale = variant.compare_at_price && parseFloat(variant.price) < parseFloat(variant.compare_at_price);
    const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

    return {
      success: true,
      product_info: {
        product_id: productData.id,
        variant_id: variant.id,
        product_name: productData.name,
        variant_name: variant.variant_name || variant.name,
        description: productData.description,
        short_description: productData.short_description,
        sku: variant.sku,
        type: productData.type,
        avatar: productData.avatar,
        images: productData.images,
        category_id: productData.category_id,

        // Thông tin giá chi tiết
        pricing: {
          original_price: originalPrice,
          final_price: finalPrice,
          is_on_sale: isOnSale,
          discount_percentage: discountPercentage,
          price_display: isOnSale ?
            `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}% từ ${originalPrice.toLocaleString('vi-VN')} VNĐ)` :
            `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
          savings: isOnSale ? originalPrice - finalPrice : 0,
          savings_display: isOnSale ? `Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ` : null,
        },

        // Thông tin tồn kho
        stock_info: {
          stock_quantity: variant.stock_quantity,
          in_stock: variant.stock_quantity > 0,
          stock_status: variant.stock_quantity > 10 ? 'Còn hàng' :
                       variant.stock_quantity > 0 ? `Chỉ còn ${variant.stock_quantity} sản phẩm` : 'Hết hàng',
        },

        attributes: variant.attributes,
        has_variants: true,
      },
      message: `✅ Biến thể "${variant.variant_name || variant.name}" - Giá: ${finalPrice.toLocaleString('vi-VN')} VNĐ${isOnSale ? ` (Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ)` : ''}`,
    };
  } else if (productData.type === 'variable' && productData.variants && productData.variants.length > 0) {
    // TRƯỜNG HỢP: Sản phẩm có nhiều biến thể
    const variants = productData.variants.map((v: any) => {
      const finalPrice = parseFloat(v.price);
      const originalPrice = v.compare_at_price ? parseFloat(v.compare_at_price) : finalPrice;
      const isOnSale = v.compare_at_price && parseFloat(v.price) < parseFloat(v.compare_at_price);
      const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

      return {
        id: v.id,
        name: v.name,
        sku: v.sku,
        pricing: {
          original_price: originalPrice,
          final_price: finalPrice,
          is_on_sale: isOnSale,
          discount_percentage: discountPercentage,
          price_display: isOnSale ?
            `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}%)` :
            `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
          savings: isOnSale ? originalPrice - finalPrice : 0,
        },
        stock_info: {
          stock_quantity: v.stock_quantity,
          in_stock: v.stock_quantity > 0,
          stock_status: v.stock_quantity > 10 ? 'Còn hàng' :
                       v.stock_quantity > 0 ? `Còn ${v.stock_quantity}` : 'Hết hàng',
        },
        attributes: v.attributes,
      };
    });

    // Tính giá range
    const prices = variants.map((v: any) => v.pricing.final_price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);

    return {
      success: true,
      product_info: {
        product_id: productData.id,
        product_name: productData.name,
        description: productData.description,
        short_description: productData.short_description,
        sku: productData.sku,
        type: productData.type,
        avatar: productData.avatar,
        images: productData.images,
        category_id: productData.category_id,

        // Thông tin giá range
        price_range: {
          min_price: minPrice,
          max_price: maxPrice,
          price_display: minPrice === maxPrice ?
            `${minPrice.toLocaleString('vi-VN')} VNĐ` :
            `${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VNĐ`,
        },

        // Thông tin tồn kho tổng
        stock_info: {
          total_stock: variants.reduce((sum: any, v: { stock_info: { stock_quantity: any; }; }) => sum + v.stock_info.stock_quantity, 0),
          available_variants: variants.filter((v: { stock_info: { in_stock: any; }; }) => v.stock_info.in_stock).length,
          total_variants: variants.length,
        },

        variants: variants,
        has_variants: true,
      },
      message: `✅ Sản phẩm "${productData.name}" có ${variants.length} biến thể. Giá từ ${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VNĐ. Vui lòng chọn biến thể cụ thể để đặt hàng.`,
    };
  } else {
    // TRƯỜNG HỢP: Sản phẩm đơn giản
    const finalPrice = parseFloat(productData.price);
    const originalPrice = productData.compare_at_price ? parseFloat(productData.compare_at_price) : finalPrice;
    const isOnSale = productData.compare_at_price && parseFloat(productData.price) < parseFloat(productData.compare_at_price);
    const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

    return {
      success: true,
      product_info: {
        product_id: productData.id,
        product_name: productData.name,
        description: productData.description,
        short_description: productData.short_description,
        sku: productData.sku,
        type: productData.type,
        avatar: productData.avatar,
        images: productData.images,
        category_id: productData.category_id,

        // Thông tin giá chi tiết
        pricing: {
          original_price: originalPrice,
          final_price: finalPrice,
          is_on_sale: isOnSale,
          discount_percentage: discountPercentage,
          price_display: isOnSale ?
            `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}% từ ${originalPrice.toLocaleString('vi-VN')} VNĐ)` :
            `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
          savings: isOnSale ? originalPrice - finalPrice : 0,
          savings_display: isOnSale ? `Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ` : null,
        },

        // Thông tin tồn kho
        stock_info: {
          stock_quantity: productData.stock_quantity,
          in_stock: productData.stock_quantity > 0,
          stock_status: productData.stock_quantity > 10 ? 'Còn hàng' :
                       productData.stock_quantity > 0 ? `Chỉ còn ${productData.stock_quantity} sản phẩm` : 'Hết hàng',
        },

        has_variants: false,
      },
      message: `✅ Sản phẩm "${productData.name}" - Giá: ${finalPrice.toLocaleString('vi-VN')} VNĐ${isOnSale ? ` (Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ)` : ''}`,
    };
  }
}
