import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { supabaseAdmin } from "../../../config/supabase";
import { optimizeOrder } from "../../../services/postgres/order-optimization.service";
import { createOrder } from "../../../services/supabase/product.service";
import { findOrCreateCustomer } from "../../../services/supabase/customer.service";

/**
 * TOOL TẠO ĐƠN HÀNG - DUY NHẤT CHO HỆ THỐNG
 *
 * Tool này tích hợp đầy đủ tính năng:
 * - Tự động lấy giá chính xác từ database (sản phẩm có/không có biến thể)
 * - Tự động tối ưu hóa khuyến mãi và vận chuyển
 * - Kiểm tra tồn kho
 * - Tạo đơn hàng với đầy đủ thông tin
 * - Tích hợp chatbot info
 */
export const createOrderTool = createTool({
  id: "create_order",
  description: "Tạo đơn hàng mới - Tool duy nhất cho hệ thống: tự động lấy giá chính xác, tối ưu khuyến mãi/vận chuyển, kiểm tra tồn kho và tạo đơn hàng",
  inputSchema: z.object({
    customer_info: z
      .object({
        name: z.string().describe("Tên khách hàng"),
        phone: z.string().describe("Số điện thoại khách hàng"),
        email: z.string().optional().describe("Email khách hàng (tùy chọn)"),
      })
      .describe("Thông tin khách hàng"),
    shipping_address: z
      .object({
        full_name: z.string().describe("Tên người nhận"),
        phone: z.string().describe("Số điện thoại người nhận"),
        address: z.string().describe("Địa chỉ chi tiết"),
        province: z.string().optional().describe("Tỉnh/Thành phố"),
        district: z.string().optional().describe("Quận/Huyện"),
        ward: z.string().nullable().optional().describe("Phường/Xã"),
        country: z.string().optional().default("Vietnam").describe("Quốc gia"),
      })
      .describe("Địa chỉ giao hàng"),
    items: z
      .array(
        z.object({
          product_id: z.string().describe("Mã sản phẩm"),
          variant_id: z
            .string()
            .nullable()
            .optional()
            .describe("Mã biến thể sản phẩm (bắt buộc cho sản phẩm có biến thể)"),
          quantity: z.number().min(1).describe("Số lượng"),
        })
      )
      .describe("Danh sách sản phẩm cần đặt hàng"),
    payment_method: z
      .string()
      .default("cod")
      .describe("Phương thức thanh toán (mặc định: cod)"),
    notes: z.string().optional().describe("Ghi chú đơn hàng"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // BƯỚC 1: Lấy giá chính xác và kiểm tra tồn kho
      const itemsWithPrices: Array<{
        product_id: string;
        variant_id?: string;
        quantity: number;
        unit_price: number;
        product_name: any;
        variant_name: any;
        sku: any;
        stock_quantity: any;
      }> = [];
      let hasError = false;
      let errorMessage = "";

      for (const item of context.items) {
        try {
          let productInfo: any = null;
          let finalPrice = 0;

          if (item.variant_id) {
            // Lấy giá từ product_variants với Supabase
            const { data: variantResult, error: variantError } = await supabaseAdmin
              .from('product_variants')
              .select(`
                id, name, sku, price, sale_price, stock_quantity, is_active, attributes,
                products!inner(name, type, is_active)
              `)
              .eq('id', item.variant_id)
              .eq('product_id', item.product_id)
              .eq('tenant_id', tenant_id)
              .eq('is_active', true)
              .eq('products.is_active', true)
              .single();

            if (variantError || !variantResult) {
              hasError = true;
              errorMessage = `❌ Không tìm thấy biến thể sản phẩm với ID: ${item.variant_id}`;
              break;
            }

            productInfo = {
              ...variantResult,
              product_name: (variantResult.products as any).name,
              product_type: (variantResult.products as any).type,
              product_is_active: (variantResult.products as any).is_active
            };
            finalPrice = productInfo.sale_price ? parseFloat(productInfo.sale_price) : parseFloat(productInfo.price);
          } else {
            // Lấy giá từ products (sản phẩm đơn giản) với Supabase
            const { data: productResult, error: productError } = await supabaseAdmin
              .from('products')
              .select('id, name, sku, price, sale_price, stock_quantity, type, is_active')
              .eq('id', item.product_id)
              .eq('tenant_id', tenant_id)
              .eq('is_active', true)
              .single();

            if (productError || !productResult) {
              hasError = true;
              errorMessage = `❌ Không tìm thấy sản phẩm với ID: ${item.product_id}`;
              break;
            }

            productInfo = productResult;

            if (productInfo.type === 'variable') {
              hasError = true;
              errorMessage = `❌ Sản phẩm "${productInfo.name}" có nhiều biến thể, vui lòng chọn biến thể cụ thể`;
              break;
            }

            finalPrice = productInfo.sale_price ? parseFloat(productInfo.sale_price) : parseFloat(productInfo.price);
          }

          // Kiểm tra tồn kho
          if (productInfo.stock_quantity < item.quantity) {
            hasError = true;
            errorMessage = `❌ Sản phẩm "${productInfo.name}" chỉ còn ${productInfo.stock_quantity} sản phẩm trong kho, không đủ cho số lượng yêu cầu ${item.quantity}`;
            break;
          }

          itemsWithPrices.push({
            product_id: item.product_id,
            variant_id: item.variant_id || undefined,
            quantity: item.quantity,
            unit_price: finalPrice,
            product_name: item.variant_id ? productInfo.product_name : productInfo.name,
            variant_name: item.variant_id ? productInfo.name : null,
            sku: productInfo.sku,
            stock_quantity: productInfo.stock_quantity,
          });


        } catch (error: any) {
          console.error(`❌ Lỗi khi lấy thông tin sản phẩm ${item.product_id}:`, error);
          hasError = true;
          errorMessage = `Lỗi khi lấy thông tin sản phẩm: ${error.message}`;
          break;
        }
      }

      if (hasError) {
        return { success: false, error: errorMessage };
      }

      // BƯỚC 2: Tối ưu hóa khuyến mãi và vận chuyển

      let best_promotion = null;
      let best_shipping = null;

      try {
        const optimizationResult = await optimizeOrder({
          orderData: {
            items: itemsWithPrices,
            shipping_address: {
              province: context.shipping_address.province,
              district: context.shipping_address.district,
            },
          },
          tenant_id: tenant_id.toString(),
        });

        if (optimizationResult.success && optimizationResult.data) {
          best_promotion = optimizationResult.data.best_promotion;
          best_shipping = optimizationResult.data.best_shipping;
        }
      } catch (error: any) {
        // Tiếp tục với giá trị mặc định
      }

      // Fallback: Sử dụng giá trị mặc định nếu không có tối ưu hóa
      if (!best_shipping) {
        best_shipping = {
          id: null,
          name: "Giao hàng tiêu chuẩn",
          calculated_fee: 30000, // Phí mặc định 30k
          estimated_delivery: "2-3 ngày làm việc"
        };
      }

      // BƯỚC 3: Tìm hoặc tạo khách hàng
      let customer_id = null;
      try {
        const customerResult = await findOrCreateCustomer({
          customerData: {
            full_name: context.customer_info.name,
            phone: context.customer_info.phone,
            email: context.customer_info.email,
          },
          tenant_id: tenant_id.toString(),
        });

        if (customerResult.success && customerResult.data) {
          customer_id = customerResult.data.id;
        }
      } catch (error: any) {
        console.error("⚠️ Lỗi khi tìm/tạo khách hàng:", error);
      }

      // BƯỚC 4: Lấy thông tin chatbot
      const resource_id = runtimeContext.get("resource_id");
      const thread_id = runtimeContext.get("thread_id");
      const bot_id = runtimeContext.get("bot_id");

      const chatbotInfo = {
        resource_id: resource_id?.toString(),
        thread_id: thread_id?.toString(),
        bot_id: bot_id?.toString(),
        created_at: new Date().toISOString(),
      };

      // BƯỚC 5: Tạo đơn hàng
      console.log("📝 Bước 5: Tạo đơn hàng với thông tin tối ưu");
      const orderResult = await createOrder({
        orderData: {
          customer_id: customer_id,
          customer_info: context.customer_info,
          shipping_address: {
            ...context.shipping_address,
            ward: context.shipping_address.ward || undefined,
          },
          items: itemsWithPrices,
          payment_method: context.payment_method,
          shipping_method: best_shipping?.name || best_shipping?.id,
          shipping_fee: best_shipping?.calculated_fee || 0,
          promotion_code: best_promotion?.code,
          notes: context.notes,
          chatbot_info: chatbotInfo,
        },
        tenant_id: tenant_id.toString(),
      });

      if (!orderResult.success) {
        return {
          success: false,
          error: "message" in orderResult ? orderResult.message : "Lỗi không xác định khi tạo đơn hàng",
        };
      }

      // Type guard để đảm bảo orderResult có data
      if (!("data" in orderResult) || !orderResult.data) {
        return {
          success: false,
          error: "Lỗi: Không nhận được dữ liệu đơn hàng từ hệ thống",
        };
      }

      // BƯỚC 6: Tính toán kết quả cuối cùng
      const totalQuantity = itemsWithPrices.reduce((total, item) => total + item.quantity, 0);
      const discountAmount = orderResult.data.discount_amount || 0;
      const shippingAmount = orderResult.data.shipping_amount || best_shipping?.calculated_fee || 0;

      console.log(`🎉 Đơn hàng đã tạo thành công: ${orderResult.data.order_number}`);

      return {
        success: true,
        order_created: {
          // Thông tin đơn hàng cơ bản
          order_id: orderResult.data.order_id,
          order_number: orderResult.data.order_number,
          status: orderResult.data.status,

          // Thông tin khách hàng
          customer_info: {
            name: context.customer_info.name,
            phone: context.customer_info.phone,
            customer_id: customer_id,
          },

          // Tóm tắt đơn hàng
          order_summary: {
            item_count: itemsWithPrices.length,
            total_quantity: totalQuantity,
            subtotal: orderResult.data.subtotal,
            shipping_amount: shippingAmount,
            discount_amount: discountAmount,
            total_amount: orderResult.data.total_amount,
          },

          // Chi tiết sản phẩm
          items_details: itemsWithPrices.map(item => ({
            product_name: item.product_name,
            variant_name: item.variant_name,
            sku: item.sku,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.unit_price * item.quantity,
          })),

          // Thông tin tối ưu hóa đã áp dụng
          optimization_applied: {
            promotion: best_promotion ? {
              code: best_promotion.code,
              name: best_promotion.name,
              discount_amount: discountAmount,
              savings_message: `🎁 Đã áp dụng khuyến mãi "${best_promotion.name}" - Tiết kiệm ${discountAmount.toLocaleString('vi-VN')} VNĐ!`,
            } : null,
            shipping: best_shipping ? {
              method: best_shipping.name,
              fee: shippingAmount,
              estimated_delivery: best_shipping.estimated_delivery,
              shipping_message: `🚚 Phương thức vận chuyển: ${best_shipping.name} - ${best_shipping.estimated_delivery}`,
            } : null,
          },

          // Thông tin khác
          payment_method: context.payment_method,
          shipping_address: context.shipping_address,
          notes: context.notes,
          chatbot_info: chatbotInfo,
        },
        message: `🎉 Đã tạo đơn hàng thành công với mã: ${orderResult.data.order_number}. Tổng tiền: ${orderResult.data.total_amount.toLocaleString('vi-VN')} VNĐ${discountAmount > 0 ? ` (Tiết kiệm ${discountAmount.toLocaleString('vi-VN')} VNĐ từ khuyến mãi!)` : ''}`,
      };
    } catch (error: any) {
      console.error("❌ Lỗi khi tạo đơn hàng:", error);
      return {
        success: false,
        error: `Lỗi khi tạo đơn hàng: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
