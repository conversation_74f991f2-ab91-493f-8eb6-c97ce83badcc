import { supabase, supabaseAdmin, isServiceKeyConfigured } from '../../config/supabase';
import { downloadAndUploadMultipleImages, uploadImageToStorage } from './storage.service';

/**
 * Service quản lý storage với tenant isolation
 * Đảm bảo user chỉ có thể truy cập hình ảnh của tenant họ
 */

/**
 * Upload hình ảnh sản phẩm với tenant isolation
 * @param imageBuffer Buffer chứa dữ liệu hình ảnh
 * @param fileName Tên file
 * @param tenant_id ID của tenant
 * @param useServiceRole Sử dụng service role (cho đồng bộ sản phẩm)
 * @returns Promise<string> URL của hình ảnh
 */
export const uploadProductImage = async (
  imageBuffer: Buffer,
  fileName: string,
  tenant_id: string,
  useServiceRole: boolean = false
): Promise<string> => {
  try {
    console.log(`[uploadProductImage] Uploading for tenant: ${tenant_id}, useServiceRole: ${useServiceRole}`);
    
    return await uploadImageToStorage(
      imageBuffer,
      fileName,
      tenant_id,
      'public', // Sử dụng bucket 'public' theo cấu hình hiện tại
      useServiceRole
    );
  } catch (error: any) {
    console.error(`[uploadProductImage] Error:`, error);
    throw new Error(`Không thể upload hình ảnh sản phẩm: ${error.message}`);
  }
};

/**
 * Upload nhiều hình ảnh sản phẩm với tenant isolation
 * @param imageUrls Mảng URL hình ảnh
 * @param tenant_id ID của tenant
 * @param useServiceRole Sử dụng service role (cho đồng bộ sản phẩm)
 * @returns Promise<string[]> Mảng URL hình ảnh đã upload
 */
export const uploadMultipleProductImages = async (
  imageUrls: string[],
  tenant_id: string,
  useServiceRole: boolean = false
): Promise<string[]> => {
  try {
    
    return await downloadAndUploadMultipleImages(
      imageUrls,
      tenant_id,
      'public', // Sử dụng bucket 'public' theo cấu hình hiện tại
      useServiceRole
    );
  } catch (error: any) {
    console.error(`[uploadMultipleProductImages] Error:`, error);
    throw new Error(`Không thể upload nhiều hình ảnh sản phẩm: ${error.message}`);
  }
};

/**
 * Lấy danh sách hình ảnh sản phẩm của tenant
 * @param tenant_id ID của tenant
 * @returns Promise<any[]> Danh sách hình ảnh
 */
export const getTenantProductImages = async (tenant_id: string): Promise<any[]> => {
  try {
    console.log(`[getTenantProductImages] Getting images for tenant: ${tenant_id}`);
    
    const { data, error } = await supabase.storage
      .from('public')
      .list(`products/${tenant_id}`);

    if (error) {
      console.error(`[getTenantProductImages] Error:`, error);
      throw new Error(`Không thể lấy danh sách hình ảnh: ${error.message}`);
    }

    console.log(`[getTenantProductImages] Found ${data?.length || 0} images for tenant: ${tenant_id}`);
    return data || [];
  } catch (error: any) {
    console.error(`[getTenantProductImages] Error:`, error);
    return [];
  }
};

/**
 * Xóa hình ảnh sản phẩm với tenant isolation
 * @param filePath Đường dẫn file (bao gồm tenant_id)
 * @param tenant_id ID của tenant (để kiểm tra quyền)
 * @returns Promise<boolean> True nếu xóa thành công
 */
export const deleteProductImage = async (
  filePath: string,
  tenant_id: string
): Promise<boolean> => {
  try {
    // Kiểm tra quyền truy cập - cập nhật để phù hợp với cấu trúc products/{tenant_id}/
    if (!filePath.startsWith(`products/${tenant_id}/`)) {
      throw new Error('Không có quyền xóa file này');
    }

    console.log(`[deleteProductImage] Deleting: ${filePath} for tenant: ${tenant_id}`);

    const { data, error } = await supabase.storage
      .from('public')
      .remove([filePath]);

    if (error) {
      console.error(`[deleteProductImage] Error:`, error);
      throw new Error(`Không thể xóa file: ${error.message}`);
    }

    console.log(`[deleteProductImage] Successfully deleted: ${filePath}`);
    return true;
  } catch (error: any) {
    console.error(`[deleteProductImage] Error:`, error);
    return false;
  }
};

/**
 * Lấy URL public của hình ảnh sản phẩm
 * @param filePath Đường dẫn file
 * @returns string URL public
 */
export const getProductImageUrl = (filePath: string): string => {
  const { data } = supabase.storage
    .from('public')
    .getPublicUrl(filePath);

  return data.publicUrl;
};

/**
 * Kiểm tra quyền truy cập file theo tenant
 * @param filePath Đường dẫn file
 * @param tenant_id ID của tenant
 * @returns boolean True nếu có quyền truy cập
 */
export const checkTenantAccess = (filePath: string, tenant_id: string): boolean => {
  try {
    // Kiểm tra xem đường dẫn có bắt đầu bằng products/{tenant_id}/ không
    return filePath.startsWith(`products/${tenant_id}/`);
  } catch (error) {
    console.error(`[checkTenantAccess] Error:`, error);
    return false;
  }
};

/**
 * Tạo đường dẫn file với tenant isolation
 * @param tenant_id ID của tenant
 * @param fileName Tên file
 * @returns string Đường dẫn file đầy đủ
 */
export const createTenantFilePath = (tenant_id: string, fileName: string): string => {
  return `products/${tenant_id}/${fileName}`;
};

/**
 * Lấy thông tin chi tiết về storage usage của tenant
 * @param tenant_id ID của tenant
 * @returns Promise<object> Thông tin storage usage
 */
export const getTenantStorageInfo = async (tenant_id: string): Promise<{
  totalFiles: number;
  totalSize: number;
  files: any[];
}> => {
  try {
    const files = await getTenantProductImages(tenant_id);
    
    const totalFiles = files.length;
    const totalSize = files.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);

    return {
      totalFiles,
      totalSize,
      files
    };
  } catch (error: any) {
    console.error(`[getTenantStorageInfo] Error:`, error);
    return {
      totalFiles: 0,
      totalSize: 0,
      files: []
    };
  }
};

export default {
  uploadProductImage,
  uploadMultipleProductImages,
  getTenantProductImages,
  deleteProductImage,
  getProductImageUrl,
  checkTenantAccess,
  createTenantFilePath,
  getTenantStorageInfo
};
