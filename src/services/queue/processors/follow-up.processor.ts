/**
 * Follow-up Processor - <PERSON><PERSON> lý các job follow-up automation
 * G<PERSON><PERSON> tin nhắn follow-up và tạo job tiếp theo trong chuỗi
 */
import { Job } from 'bullmq';
import {
  FollowUpJobData,
  FollowUpJobResult,
  FOLLOW_UP_JOB_NAMES,
  ConversationFollowUpState
} from '../types/follow-up.types';
import {
  getConversationFollowUpState,
  saveConversationFollowUpState,
  createFollowUpJob
} from '../follow-up.service';
import { sendChatwootMessage } from '../../message/chatwoot-message.service';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Xử lý job gửi follow-up message
 */
export const processFollowUpJob = async (job: Job<FollowUpJobData>): Promise<FollowUpJobResult> => {
  const { 
    conversation_id, 
    tenant_id, 
    channel_id, 
    account_id, 
    rule_id, 
    rule_order, 
    message, 
    next_rule 
  } = job.data;


  try {
    // L<PERSON>y trạng thái hiện tại của conversation
    const state = await getConversationFollowUpState(conversation_id);
    
    if (!state || !state.is_active) {
      return {
        success: false,
        conversation_id,
        rule_id,
        message_sent: false,
        next_job_created: false,
        error: 'Conversation không active hoặc không tồn tại state',
      };
    }

    // Kiểm tra xem rule này đã được gửi chưa (tránh trùng lặp)
    if (state.sent_rule_ids.includes(rule_id)) {
      return {
        success: false,
        conversation_id,
        rule_id,
        message_sent: false,
        next_job_created: false,
        error: 'Rule đã được gửi trước đó',
      };
    }

    // Debug log chi tiết để kiểm tra job data
      rule_id: job.data.rule_id,
      message: job.data.message,
      has_formatting: !!job.data.message_formatting,
      formatting_details: job.data.message_formatting
    });

    // Kiểm tra chi tiết message_formatting
    if (job.data.message_formatting) {
        has_images: !!job.data.message_formatting.images,
        images_count: job.data.message_formatting.images?.length || 0,
        images_data: job.data.message_formatting.images,
        has_buttons: !!job.data.message_formatting.buttons,
        buttons_count: job.data.message_formatting.buttons?.length || 0,
        buttons_data: job.data.message_formatting.buttons
      });
    } else {
    }

    // Gửi tin nhắn follow-up
    const messageResult = await sendChatwootMessage({
      accountId: account_id,
      conversationId: conversation_id,
      content: message,
      messageFormatting: job.data.message_formatting
    });

      success: messageResult.success,
      images_sent: messageResult.imagesSent,
      text_sent: messageResult.textSent,
      error: messageResult.error
    });

    const messageSent = messageResult.success;

    if (!messageSent) {
      console.error(`❌ Không thể gửi follow-up message cho conversation ${conversation_id}`);
      return {
        success: false,
        conversation_id,
        rule_id,
        message_sent: false,
        next_job_created: false,
        error: 'Không thể gửi tin nhắn follow-up',
      };
    }


    // Cập nhật state: thêm rule_id vào danh sách đã gửi
    state.sent_rule_ids.push(rule_id);
    // Không cần cập nhật current_rule_index ở đây vì nó sẽ được cập nhật khi tạo job tiếp theo
    state.active_job_id = undefined; // Clear job ID hiện tại

    let nextJobCreated = false;
    let nextJobId: string | undefined;

    // Tạo job tiếp theo nếu có
    if (next_rule) {

      const nextJobResult = await createFollowUpJob({
        conversation_id,
        tenant_id,
        channel_id,
        account_id,
        rule_id: next_rule.id, // Truyền rule_id thay vì rule_index
        reset_state: false,
        skip_cancel_current: true, // Bỏ qua việc hủy job hiện tại vì đang trong quá trình xử lý
      });

      if (nextJobResult.success && nextJobResult.data) {
        nextJobCreated = true;
        nextJobId = nextJobResult.data.jobId;
        state.active_job_id = nextJobId;
      } else {
        console.error(`❌ Không thể tạo job tiếp theo:`, nextJobResult.error);
      }
    } else {
      state.is_active = false; // Đánh dấu hoàn thành
    }

    // Lưu state đã cập nhật
    await saveConversationFollowUpState(state);

    return {
      success: true,
      conversation_id,
      rule_id,
      message_sent: true,
      next_job_created: nextJobCreated,
      next_job_id: nextJobId,
    };

  } catch (error: any) {
    console.error(`❌ Lỗi khi xử lý follow-up job ${job.id}:`, error);
    
    return {
      success: false,
      conversation_id,
      rule_id,
      message_sent: false,
      next_job_created: false,
      error: error.message,
    };
  }
};



/**
 * Xử lý job processor chính
 */
export const followUpJobProcessor = async (job: Job<FollowUpJobData>) => {
  const jobName = job.name;
  
  switch (jobName) {
    case FOLLOW_UP_JOB_NAMES.SEND_FOLLOW_UP:
      return await processFollowUpJob(job);
    
    default:
      throw new Error(`Unknown follow-up job type: ${jobName}`);
  }
};

/**
 * Xử lý job failed
 */
export const handleFailedFollowUpJob = async (job: Job<FollowUpJobData>, error: Error) => {
  const { conversation_id, rule_order } = job.data;
  
  console.error(`❌ Follow-up job ${job.id} failed cho conversation ${conversation_id}, rule ${rule_order}:`, error);
  
  try {
    // Lấy state và đánh dấu job failed
    const state = await getConversationFollowUpState(conversation_id);
    if (state) {
      state.active_job_id = undefined; // Clear failed job ID
      await saveConversationFollowUpState(state);
    }
    
    
  } catch (cleanupError) {
    console.error('❌ Lỗi khi cleanup failed follow-up job:', cleanupError);
  }
};

export default {
  processFollowUpJob,
  followUpJobProcessor,
  handleFailedFollowUpJob,
};
