import { sql } from "../../config/postgres";

/**
 * L<PERSON><PERSON> danh sách khuyến mãi đang hoạt động
 * @param tenant_id ID của tenant
 * @param promotion_code Mã khuyến mãi cụ thể (nếu có)
 * @param product_id ID sản phẩm để lọc khuyến mãi áp dụng cho sản phẩm cụ thể (nếu có)
 * @param category_id ID danh mục để lọc khuyến mãi áp dụng cho danh mục cụ thể (nếu có)
 */
export const getActivePromotions = async ({
  tenant_id,
  promotion_code,
  product_id,
  category_id,
}: {
  tenant_id: string;
  promotion_code?: string;
  product_id?: string;
  category_id?: string;
}) => {
  try {
    console.log(`[getActivePromotions] Lấy khuyến mãi đang hoạt động cho tenant: ${tenant_id}`);
    console.log(`[getActivePromotions] <PERSON><PERSON><PERSON> bộ lọc: promotion_code=${promotion_code}, product_id=${product_id}, category_id=${category_id}`);

    // Xây dựng truy vấn SQL động
    let queryConditions = [];
    let queryParams: any[] = [tenant_id];

    // Điều kiện cơ bản: tenant_id, is_active, thời gian hiện tại nằm trong khoảng start_date và end_date
    queryConditions.push(`tenant_id = $1`);
    queryConditions.push(`is_active = true`);
    queryConditions.push(`(start_date IS NULL OR start_date <= NOW())`);
    queryConditions.push(`(end_date IS NULL OR end_date >= NOW())`);

    // Áp dụng các bộ lọc
    if (promotion_code) {
      queryConditions.push(`code = $${queryParams.length + 1}`);
      queryParams.push(promotion_code);
    }

    // Xây dựng câu truy vấn SQL hoàn chỉnh
    const whereClause = queryConditions.length > 0 ? `WHERE ${queryConditions.join(' AND ')}` : '';

    // Thực hiện truy vấn SQL
    const query = `
      SELECT
        id,
        name,
        description,
        code,
        type,
        value_type,
        value,
        min_purchase_amount,
        max_discount_amount,
        usage_limit,
        usage_count,
        start_date,
        end_date,
        is_active,
        applies_to,
        eligible_product_ids,
        eligible_category_ids,
        created_at,
        updated_at
      FROM promotions
      ${whereClause}
      ORDER BY created_at DESC
    `;

    console.log(`[getActivePromotions] Thực thi truy vấn SQL:`, query);
    console.log(`[getActivePromotions] Với tham số:`, queryParams);

    // Thực hiện truy vấn với postgres
    const promotions = await sql.unsafe(query, queryParams);


    // Lọc thêm theo product_id hoặc category_id nếu có
    let filteredPromotions = promotions ? [...promotions] : [];

    if (product_id) {
      filteredPromotions = filteredPromotions.filter(promo => {
        // Nếu áp dụng cho tất cả
        if (promo.applies_to === 'all') return true;

        // Nếu áp dụng cho sản phẩm cụ thể
        if (promo.applies_to === 'products' && promo.eligible_product_ids) {
          try {
            const productIds = Array.isArray(promo.eligible_product_ids)
              ? promo.eligible_product_ids
              : JSON.parse(promo.eligible_product_ids);
            return Array.isArray(productIds) && productIds.includes(product_id);
          } catch (error: any) {
            console.error(`Lỗi khi parse eligible_product_ids: ${error?.message || 'Lỗi không xác định'}`, promo.eligible_product_ids);
            return false;
          }
        }

        return false;
      });
    }

    if (category_id) {
      filteredPromotions = filteredPromotions.filter(promo => {
        // Nếu áp dụng cho tất cả
        if (promo.applies_to === 'all') return true;

        // Nếu áp dụng cho danh mục cụ thể
        if (promo.applies_to === 'categories' && promo.eligible_category_ids) {
          try {
            const categoryIds = Array.isArray(promo.eligible_category_ids)
              ? promo.eligible_category_ids
              : JSON.parse(promo.eligible_category_ids);
            return Array.isArray(categoryIds) && categoryIds.includes(category_id);
          } catch (error: any) {
            console.error(`Lỗi khi parse eligible_category_ids: ${error?.message || 'Lỗi không xác định'}`, promo.eligible_category_ids);
            return false;
          }
        }

        return false;
      });
    }

    // Định dạng lại dữ liệu để dễ hiểu hơn cho LLM
    const formattedPromotions = filteredPromotions.map(promo => {
      // Định dạng giá trị khuyến mãi
      let valueDisplay = '';
      if (promo.value_type === 'percentage' && promo.value != null) {
        valueDisplay = `${promo.value}%`;
      } else if (promo.value_type === 'fixed' && promo.value != null) {
        try {
          valueDisplay = `${promo.value.toLocaleString('vi-VN')} VNĐ`;
        } catch (error) {
          valueDisplay = `${promo.value} VNĐ`;
        }
      }

      // Định dạng thời gian áp dụng
      let timeDisplay = 'Không giới hạn';
      if (promo.start_date && promo.end_date) {
        const startDate = new Date(promo.start_date).toLocaleDateString('vi-VN');
        const endDate = new Date(promo.end_date).toLocaleDateString('vi-VN');
        timeDisplay = `Từ ${startDate} đến ${endDate}`;
      } else if (promo.start_date) {
        const startDate = new Date(promo.start_date).toLocaleDateString('vi-VN');
        timeDisplay = `Từ ${startDate}`;
      } else if (promo.end_date) {
        const endDate = new Date(promo.end_date).toLocaleDateString('vi-VN');
        timeDisplay = `Đến ${endDate}`;
      }

      // Định dạng điều kiện áp dụng
      let conditionDisplay = '';
      if (promo.min_purchase_amount) {
        try {
          conditionDisplay += `Đơn hàng từ ${promo.min_purchase_amount.toLocaleString('vi-VN')} VNĐ. `;
        } catch (error) {
          conditionDisplay += `Đơn hàng từ ${promo.min_purchase_amount} VNĐ. `;
        }
      }
      if (promo.max_discount_amount) {
        try {
          conditionDisplay += `Giảm tối đa ${promo.max_discount_amount.toLocaleString('vi-VN')} VNĐ. `;
        } catch (error) {
          conditionDisplay += `Giảm tối đa ${promo.max_discount_amount} VNĐ. `;
        }
      }
      if (promo.usage_limit) {
        conditionDisplay += `Giới hạn ${promo.usage_limit} lần sử dụng. `;
      }
      if (!conditionDisplay) {
        conditionDisplay = 'Không có điều kiện đặc biệt.';
      }

      // Định dạng đối tượng áp dụng
      let appliesDisplay = '';
      if (promo.applies_to === 'all') {
        appliesDisplay = 'Áp dụng cho tất cả sản phẩm';
      } else if (promo.applies_to === 'products' && promo.eligible_product_ids) {
        appliesDisplay = 'Áp dụng cho một số sản phẩm cụ thể';
      } else if (promo.applies_to === 'categories' && promo.eligible_category_ids) {
        appliesDisplay = 'Áp dụng cho một số danh mục cụ thể';
      }

      return {
        id: promo.id,
        name: promo.name,
        description: promo.description,
        code: promo.code,
        type: promo.type,
        value_type: promo.value_type,
        value: promo.value,
        value_display: valueDisplay,
        min_purchase_amount: promo.min_purchase_amount,
        max_discount_amount: promo.max_discount_amount,
        usage_limit: promo.usage_limit,
        usage_count: promo.usage_count,
        start_date: promo.start_date,
        end_date: promo.end_date,
        time_display: timeDisplay,
        condition_display: conditionDisplay,
        applies_to: promo.applies_to,
        applies_display: appliesDisplay,
        eligible_product_ids: promo.eligible_product_ids,
        eligible_category_ids: promo.eligible_category_ids,
      };
    });

    return {
      success: true,
      data: formattedPromotions,
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách khuyến mãi:', error);
    return {
      success: false,
      message: `Lỗi khi lấy danh sách khuyến mãi: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
